"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Accordion, 
  AccordionContent, 
  AccordionItem, 
  AccordionTrigger 
} from "@/components/ui/accordion";
import { 
  MoreVertical, 
  Trash2, 
  Copy, 
  GripVertical,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CodeEditor } from "@/components/ui/code-editor";
import { RuleSection } from "@/lib/store";
import { validateSection, getSectionSummary } from "@/lib/rule-sections";

interface RuleSectionEditorProps {
  section: RuleSection;
  index: number;
  totalSections: number;
  isExpanded: boolean;
  onUpdate: (section: RuleSection) => void;
  onDelete: () => void;
  onDuplicate: () => void;
  onMoveUp: () => void;
  onMoveDown: () => void;
  onToggleExpanded: () => void;
}

export function RuleSectionEditor({
  section,
  index,
  totalSections,
  isExpanded,
  onUpdate,
  onDelete,
  onDuplicate,
  onMoveUp,
  onMoveDown,
  onToggleExpanded,
}: RuleSectionEditorProps) {
  const [errors, setErrors] = useState<string[]>([]);

  const handleFieldChange = (field: keyof RuleSection, value: string) => {
    const updatedSection = { ...section, [field]: value };
    onUpdate(updatedSection);
    
    // Validate on change
    const validationErrors = validateSection(updatedSection);
    setErrors(validationErrors);
  };

  const sectionSummary = getSectionSummary(section);
  const hasErrors = errors.length > 0;

  return (
    <Card className={`transition-all ${hasErrors ? 'border-red-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            <div className="flex items-center gap-1">
              <GripVertical className="h-4 w-4 text-muted-foreground cursor-grab" />
              <span className="text-sm font-medium text-muted-foreground">
                {index + 1}
              </span>
            </div>
            
            <div className="flex-1">
              <CardTitle className="text-base">
                {section.title || `Section ${index + 1}`}
              </CardTitle>
              {!isExpanded && (
                <p className="text-sm text-muted-foreground mt-1 truncate">
                  {sectionSummary}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Move buttons */}
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveUp}
                disabled={index === 0}
                className="h-8 w-8 p-0"
              >
                <ChevronUp className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="1"
                onClick={onMoveDown}
                disabled={index === totalSections - 1}
                className="h-8 w-8 p-0"
              >
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>

            {/* Actions dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger>
                <Button variant="ghost" size="1" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={onDuplicate}>
                  <Copy className="mr-2 h-4 w-4" />
                  Duplicate
                </DropdownMenuItem>
                <DropdownMenuItem 
                  onClick={onDelete}
                  disabled={totalSections === 1}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Expand/Collapse button */}
            <Button
              variant="ghost"
              size="1"
              onClick={onToggleExpanded}
              className="h-8 w-8 p-0"
            >
              {isExpanded ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>

        {hasErrors && (
          <div className="text-sm text-red-600 mt-2">
            {errors.map((error, i) => (
              <div key={i}>• {error}</div>
            ))}
          </div>
        )}
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-4">
          {/* Section Title */}
          <div>
            <Label htmlFor={`section-title-${section.id}`}>Section Title</Label>
            <Input
              id={`section-title-${section.id}`}
              value={section.title}
              onChange={(e) => handleFieldChange('title', e.target.value)}
              placeholder="Enter section title..."
            />
          </div>

          {/* Optional Metadata */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor={`section-name-${section.id}`}>Name (Optional)</Label>
              <Input
                id={`section-name-${section.id}`}
                value={section.name || ''}
                onChange={(e) => handleFieldChange('name', e.target.value)}
                placeholder="e.g., global, stylesheet"
              />
            </div>

            <div>
              <Label htmlFor={`section-globs-${section.id}`}>File Patterns (Optional)</Label>
              <Input
                id={`section-globs-${section.id}`}
                value={section.globs || ''}
                onChange={(e) => handleFieldChange('globs', e.target.value)}
                placeholder="e.g., **.css, *.js"
              />
            </div>

            <div>
              <Label htmlFor={`section-description-${section.id}`}>Description (Optional)</Label>
              <Input
                id={`section-description-${section.id}`}
                value={section.description || ''}
                onChange={(e) => handleFieldChange('description', e.target.value)}
                placeholder="Brief description"
              />
            </div>
          </div>

          {/* Section Content */}
          <div>
            <Label htmlFor={`section-content-${section.id}`}>Content</Label>
            <CodeEditor
              value={section.content}
              onChange={(value) => handleFieldChange('content', value)}
              placeholder="Enter section content (markdown supported)..."
            />
          </div>
        </CardContent>
      )}
    </Card>
  );
}
